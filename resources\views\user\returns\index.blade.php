@extends('layouts.user')

@section('title', 'Retur Produk - Dashboard Toko')
@section('page-title', 'Retur Produk')

@section('content')
<div class="user-dashboard-returns-container">
    <!-- Header with Actions -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <div class="user-dashboard-card-header-content">
                <h1 class="user-dashboard-card-title">Retur Produk</h1>
                <p class="user-dashboard-card-subtitle"><PERSON><PERSON><PERSON> permintaan retur produk rusak atau bermasalah</p>
            </div>
            <!-- <div class="user-dashboard-card-actions">
                <a href="{{ route('user.returns.create') }}" class="user-dashboard-btn user-dashboard-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Buat Retur
                </a>
            </div> -->
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="user-dashboard-stats-grid">
        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-icon user-dashboard-stat-icon-blue">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-content">
                <p class="user-dashboard-stat-label">Total Retur</p>
                <p class="user-dashboard-stat-value">{{ number_format($stats['total']) }}</p>
            </div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-icon user-dashboard-stat-icon-yellow">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-content">
                <p class="user-dashboard-stat-label">Menunggu Persetujuan</p>
                <p class="user-dashboard-stat-value">{{ number_format($stats['requested']) }}</p>
            </div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-icon user-dashboard-stat-icon-blue">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-content">
                <p class="user-dashboard-stat-label">Disetujui</p>
                <p class="user-dashboard-stat-value">{{ number_format($stats['approved']) }}</p>
            </div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-icon user-dashboard-stat-icon-green">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-content">
                <p class="user-dashboard-stat-label">Selesai</p>
                <p class="user-dashboard-stat-value">{{ number_format($stats['completed']) }}</p>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-content">
            <form method="GET" class="user-dashboard-filter-form">
                <!-- Month Filter -->
                <div class="user-dashboard-form-group">
                    <label class="user-dashboard-form-label">Periode</label>
                    <select name="month" class="user-dashboard-form-select">
                        @foreach($availableMonths as $month)
                            <option value="{{ $month['value'] }}" {{ ($filterMonth ?? 'all') === $month['value'] ? 'selected' : '' }}>
                                {{ $month['label'] }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <!-- Search -->
                <div class="user-dashboard-form-group">
                    <label class="user-dashboard-form-label">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari nama produk..."
                           class="user-dashboard-form-input">
                </div>

                <!-- Status Filter -->
                <div class="user-dashboard-form-group">
                    <label class="user-dashboard-form-label">Status</label>
                    <select name="status" class="user-dashboard-form-select">
                        <option value="">Semua Status</option>
                        <option value="requested" {{ request('status') === 'requested' ? 'selected' : '' }}>Diminta</option>
                        <option value="approved" {{ request('status') === 'approved' ? 'selected' : '' }}>Disetujui</option>
                        <option value="in_transit" {{ request('status') === 'in_transit' ? 'selected' : '' }}>Dalam Perjalanan</option>
                        <option value="completed" {{ request('status') === 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="rejected" {{ request('status') === 'rejected' ? 'selected' : '' }}>Ditolak</option>
                    </select>
                </div>

                <!-- Filter Button -->
                <div class="user-dashboard-form-group user-dashboard-form-group-button">
                    <button type="submit" class="user-dashboard-btn user-dashboard-btn-primary user-dashboard-btn-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Daftar Retur</h2>
        </div>
        <div class="user-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name }}</div>
                                @if($return->supplier)
                                <div class="text-sm text-gray-500">Ke: {{ $return->supplier->name }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($return->quantity) }}</div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $return->reason_in_indonesian }}</div>
                                @if($return->description)
                                <div class="text-xs text-gray-500 mt-1">{{ Str::limit($return->description, 50) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ auth()->user()->formatDate($return->return_date) }}</div>
                                @if($return->approved_date)
                                <div class="text-xs text-gray-500">Disetujui: {{ auth()->user()->formatDate($return->approved_date) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <span class="user-dashboard-status-badge user-dashboard-status-{{ $return->status }}">
                                    {{ $return->status_in_indonesian }}
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <a href="{{ route('user.returns.show', $return) }}"
                                   class="user-dashboard-btn user-dashboard-btn-secondary user-dashboard-btn-sm">
                                    Lihat Detail
                                </a>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="user-dashboard-empty-state">
                                    <svg class="user-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="user-dashboard-empty-title">Belum ada retur</p>
                                    <p class="user-dashboard-empty-description">Mulai dengan membuat permintaan retur untuk produk bermasalah</p>
                                    <!-- <a href="{{ route('user.returns.create') }}" class="user-dashboard-btn user-dashboard-btn-primary">
                                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                                        </svg>
                                        Buat Retur
                                    </a> -->
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if($returns->hasPages())
            <div class="user-dashboard-pagination">
                {{ $returns->links() }}
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
