<?php

namespace App\Http\Controllers\Supplier;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SupplierDelivery;
use App\Models\ReturnModel;
use App\Models\Supplier;
use Carbon\Carbon;
use App\Traits\SupplierHelper;

class SupplierDashboardController extends Controller
{
    use SupplierHelper;
    /**
     * Display the supplier dashboard.
     */
    public function index(Request $request)
    {
        // Get current supplier using the helper trait
        $supplier = $this->getCurrentSupplier();

        // Get filter parameters - show all data by default
        $filterMonth = $request->get('month');
        $startDate = null;
        $endDate = null;

        if ($filterMonth && $filterMonth !== 'all') {
            // Parse the filter month only when specified
            $monthDate = Carbon::createFromFormat('Y-m', $filterMonth);
            $startDate = $monthDate->startOfMonth()->toDateString();
            $endDate = $monthDate->endOfMonth()->toDateString();
        }

        // Get delivery statistics for this supplier - apply conditional filtering
        $deliveryQuery = SupplierDelivery::where('supplier_id', $supplier->id);
        if ($filterMonth && $filterMonth !== 'all') {
            $deliveryQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        }

        $deliveryStats = [
            'total_deliveries' => (clone $deliveryQuery)->count(),
            'pending_deliveries' => (clone $deliveryQuery)->where('status', 'pending')->count(),
            'received_deliveries' => (clone $deliveryQuery)->where('status', 'received')->count(),
            'partial_deliveries' => (clone $deliveryQuery)->where('status', 'partial')->count(),
        ];

        // Get return statistics for this supplier - apply conditional filtering
        $returnQuery = ReturnModel::where('supplier_id', $supplier->id);
        if ($filterMonth && $filterMonth !== 'all') {
            $returnQuery->whereBetween('return_date', [$startDate, $endDate]);
        }

        $returnStats = [
            'total_returns' => (clone $returnQuery)->count(),
            'pending_returns' => (clone $returnQuery)->where('status', 'requested')->count(),
            'approved_returns' => (clone $returnQuery)->where('status', 'approved')->count(),
            'completed_returns' => (clone $returnQuery)->where('status', 'completed')->count(),
        ];

        // Get recent deliveries for this supplier - apply conditional filtering
        $recentDeliveriesQuery = SupplierDelivery::with(['supplier', 'product'])
            ->where('supplier_id', $supplier->id);
        if ($filterMonth && $filterMonth !== 'all') {
            $recentDeliveriesQuery->whereBetween('delivery_date', [$startDate, $endDate]);
        }
        $recentDeliveries = $recentDeliveriesQuery->orderBy('delivery_date', 'desc')
            ->limit(10)
            ->get();

        // Get recent returns for this supplier - apply conditional filtering
        $recentReturnsQuery = ReturnModel::with(['product', 'store', 'supplier'])
            ->where('supplier_id', $supplier->id);
        if ($filterMonth && $filterMonth !== 'all') {
            $recentReturnsQuery->whereBetween('return_date', [$startDate, $endDate]);
        }
        $recentReturns = $recentReturnsQuery->orderBy('return_date', 'desc')
            ->limit(10)
            ->get();

        // Get active suppliers count (global stat)
        $activeSuppliersCount = Supplier::where('status', 'active')->count();

        // Get available months for filter dropdown
        $availableMonths = $this->getAvailableMonths($supplier->id);

        return view('supplier.dashboard', compact(
            'deliveryStats',
            'returnStats',
            'recentDeliveries',
            'recentReturns',
            'activeSuppliersCount',
            'filterMonth',
            'supplier',
            'availableMonths'
        ));
    }

    /**
     * Get available months for filter dropdown.
     */
    private function getAvailableMonths($supplierId)
    {
        $months = [];

        // Add "All Time" option
        $months[] = [
            'value' => 'all',
            'label' => 'Semua Waktu'
        ];

        // Get months from existing deliveries
        $deliveryMonths = SupplierDelivery::where('supplier_id', $supplierId)
            ->selectRaw('DATE_FORMAT(delivery_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        // Get months from existing returns
        $returnMonths = ReturnModel::where('supplier_id', $supplierId)
            ->selectRaw('DATE_FORMAT(return_date, "%Y-%m") as month')
            ->distinct()
            ->orderBy('month', 'desc')
            ->pluck('month');

        // Combine and deduplicate months
        $allMonths = $deliveryMonths->merge($returnMonths)->unique()->sort()->reverse();

        foreach ($allMonths as $month) {
            $date = Carbon::createFromFormat('Y-m', $month);
            $months[] = [
                'value' => $month,
                'label' => $date->format('F Y')
            ];
        }

        return $months;
    }
}
