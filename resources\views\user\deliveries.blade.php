@extends('layouts.user')

@section('title', 'Distribusi - Indah Berkah Abadi')

@push('styles')
<!-- Independent CSS for User Dashboard Dropdowns -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-dropdowns.css') }}">
<!-- Independent CSS for User Dashboard Deliveries -->
<link rel="stylesheet" href="{{ asset('css/user-dashboard-deliveries.css') }}">
@endpush

@section('content')
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Distribusi</h1>
            <p class="user-dashboard-header-subtitle">Pantau distribusi dari gudang ke toko Anda</p>
        </div>
    </div>

    <!-- Time Period Filter -->
    @include('user.components.time-period-filter')

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ number_format($stats['total']) }}</div>
                        <div class="text-xs text-gray-600">Total Distribusi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ number_format($stats['pending']) }}</div>
                        <div class="text-xs text-gray-600">Menunggu Konfirmasi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ number_format($stats['confirmed']) }}</div>
                        <div class="text-xs text-gray-600">Dikonfirmasi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m5 14v-5a2 2 0 00-2-2H6a2 2 0 00-2 2v5a2 2 0 002 2h14a2 2 0 002-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900">{{ number_format($stats['with_shortage']) }}</div>
                        <div class="text-xs text-gray-600">Dengan Kekurangan</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="user-dashboard-search-container">
        <div class="user-dashboard-card-content">
            <form method="GET" class="user-dashboard-search-form">
                <!-- Preserve time period parameter -->
                @if(request('period'))
                    <input type="hidden" name="period" value="{{ request('period') }}">
                @endif
                <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                    <label for="status-filter" class="user-dashboard-form-label">Status</label>
                    <select id="status-filter" name="status" class="user-dashboard-form-select user-dashboard-form-select-enhanced">
                        <option value="">Semua Status</option>
                        @foreach($statuses as $value => $label)
                            <option value="{{ $value }}" {{ request('status') == $value ? 'selected' : '' }}>
                                {{ $label }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                    <label for="month-filter" class="user-dashboard-form-label">Periode</label>
                    <select id="month-filter" name="month" class="user-dashboard-form-select user-dashboard-form-select-enhanced">
                        @foreach($availableMonths as $month)
                            <option value="{{ $month['value'] }}" {{ ($filterMonth ?? 'all') === $month['value'] ? 'selected' : '' }}>
                                {{ $month['label'] }}
                            </option>
                        @endforeach
                    </select>
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center w-full user-dashboard-btn-sm">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Distributions Table -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Daftar Distribusi</h2>
            <p class="user-dashboard-card-description">{{ $deliveries->total() }} distribusi ditemukan</p>
        </div>
        <div class="user-dashboard-card-content">
            @if($deliveries->count() > 0)
                <div class="user-dashboard-deliveries-table-container">
                    <table class="user-dashboard-deliveries-table">
                        <thead class="user-dashboard-deliveries-table-header">
                            <tr>
                                <th class="user-dashboard-deliveries-table-th">Produk</th>
                                <th class="user-dashboard-deliveries-table-th">Jumlah</th>
                                <th class="user-dashboard-deliveries-table-th">Tanggal Distribusi</th>
                                <th class="user-dashboard-deliveries-table-th">Status</th>
                                <th class="user-dashboard-deliveries-table-th">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($deliveries as $delivery)
                            <tr class="user-dashboard-deliveries-table-row">
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-product-info">
                                        <div class="user-dashboard-deliveries-product-name">{{ $delivery->product ? $delivery->product->name : 'Produk tidak diketahui' }}</div>
                                        @if($delivery->confirmed && $delivery->notes)
                                        <div class="user-dashboard-deliveries-product-notes">{{ Str::limit($delivery->notes, 50) }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-quantity-info">
                                        <div class="user-dashboard-deliveries-quantity-main">{{ number_format($delivery->quantity) }} unit</div>
                                        @if($delivery->confirmed && $delivery->received_quantity !== null)
                                        <div class="user-dashboard-deliveries-quantity-received">
                                            Diterima: {{ number_format($delivery->received_quantity) }} unit
                                            @if($delivery->received_quantity != $delivery->quantity)
                                            <span class="user-dashboard-deliveries-quantity-difference">
                                                @if($delivery->received_quantity < $delivery->quantity)
                                                    (kurang {{ number_format($delivery->quantity - $delivery->received_quantity) }})
                                                @else
                                                    (lebih {{ number_format($delivery->received_quantity - $delivery->quantity) }})
                                                @endif
                                            </span>
                                            @endif
                                        </div>
                                        @endif
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-date-info">
                                        <div class="user-dashboard-deliveries-date-main">{{ $delivery->date_distributed ? $delivery->date_distributed->format('d M Y') : 'Belum dijadwalkan' }}</div>
                                        @if($delivery->confirmed && $delivery->confirmed_at)
                                        <div class="user-dashboard-deliveries-date-confirmed">Dikonfirmasi: {{ $delivery->confirmed_at->format('d M Y') }}</div>
                                        @endif
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <span class="user-dashboard-deliveries-status-badge user-dashboard-deliveries-status-{{ $delivery->confirmed ? 'confirmed' : 'pending' }}">
                                        {{ $delivery->confirmed ? 'Dikonfirmasi' : 'Menunggu Konfirmasi' }}
                                    </span>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-actions">
                                        <a href="{{ route('user.deliveries.show', $delivery->id) }}"
                                           class="user-dashboard-deliveries-action-btn user-dashboard-deliveries-action-view">
                                            Lihat Detail
                                        </a>
                                        @if($delivery->max_returnable_quantity > 0)
                                        <button onclick="openReturnModal('{{ $delivery->id }}', '{{ $delivery->product->name }}', '{{ $delivery->max_returnable_quantity }}', '{{ $delivery->product_id }}')"
                                                class="user-dashboard-deliveries-action-btn user-dashboard-deliveries-action-return">
                                            Retur
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="user-dashboard-deliveries-pagination">
                    {{ $deliveries->withQueryString()->links() }}
                </div>
            @else
                <div class="user-dashboard-deliveries-empty">
                    <svg class="user-dashboard-deliveries-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    <h3 class="user-dashboard-deliveries-empty-title">Tidak ada distribusi</h3>
                    <p class="user-dashboard-deliveries-empty-description">Belum ada distribusi yang ditemukan dengan filter yang dipilih.</p>
                </div>
            @endif
        </div>
    </div>
</div>



<!-- Return Modal -->
<div id="returnModal" class="user-dashboard-deliveries-modal">
    <div class="user-dashboard-deliveries-modal-content">
        <div class="user-dashboard-deliveries-modal-header">
            <h3 class="user-dashboard-deliveries-modal-title">Buat Permintaan Retur</h3>
            <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="returnForm" method="POST" action="{{ route('user.returns.store-from-distribution') }}">
            @csrf
            <input type="hidden" id="distribution_id" name="distribution_id" value="">
            <input type="hidden" id="product_id" name="product_id" value="">

            <div class="user-dashboard-deliveries-modal-body">
                <div class="user-dashboard-deliveries-form-group">
                    <div class="user-dashboard-deliveries-info-box">
                        <div class="user-dashboard-deliveries-info-content">
                            <svg class="user-dashboard-deliveries-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="user-dashboard-deliveries-info-text">Buat retur untuk produk <strong id="returnProductName"></strong> yang tidak diterima sesuai jumlah yang dikirim.</p>
                        </div>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_quantity" class="user-dashboard-deliveries-form-label">
                            Jumlah Retur (Maksimal: <span id="maxReturnQuantity"></span>)
                        </label>
                        <input type="number"
                               id="return_quantity"
                               name="quantity"
                               class="user-dashboard-deliveries-form-input"
                               min="1"
                               value="1"
                               required>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_reason" class="user-dashboard-deliveries-form-label">
                            Alasan Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <select id="return_reason" name="reason" class="user-dashboard-deliveries-form-select" required>
                            <option value="">Pilih Alasan</option>
                            <option value="damaged">Rusak</option>
                            <option value="expired">Kadaluarsa</option>
                            <option value="defective">Cacat</option>
                            <option value="overstock">Kelebihan Stok</option>
                            <option value="shortage">Kekurangan Pengiriman</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_description" class="user-dashboard-deliveries-form-label">
                            Deskripsi Detail <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <textarea id="return_description"
                                  name="description"
                                  rows="3"
                                  class="user-dashboard-deliveries-form-textarea"
                                  placeholder="Jelaskan detail masalah atau alasan retur..."
                                  required></textarea>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_date" class="user-dashboard-deliveries-form-label">
                            Tanggal Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <input type="date"
                               id="return_date"
                               name="return_date"
                               class="user-dashboard-deliveries-form-input"
                               max="{{ date('Y-m-d') }}"
                               value="{{ date('Y-m-d') }}"
                               required>
                    </div>
                </div>
            </div>

            <div class="user-dashboard-deliveries-modal-footer">
                <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-outline">
                    Batal
                </button>
                <button type="submit" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-primary">
                    Buat Retur
                </button>
            </div>
        </form>
    </div>
</div>

@push('scripts')
<!-- Independent JavaScript for User Dashboard Dropdowns -->
<script src="{{ asset('js/user-dashboard-dropdowns.js') }}"></script>
<!-- Independent JavaScript for User Dashboard Deliveries -->
<script src="{{ asset('js/user-dashboard-deliveries.js') }}"></script>
@endpush

@endsection
