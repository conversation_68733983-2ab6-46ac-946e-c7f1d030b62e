<?php $__env->startSection('title', 'Ke<PERSON><PERSON> - Indah Berkah Abadi'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header with Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Kelola Retur</h1>
                    <p class="text-gray-600 mt-1">Kelola retur produk dari toko ke supplier</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.returns.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Buat Retur Baru
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total'] ?? 0)); ?></p>
                    <p class="admin-dashboard-stat-label">Total Retur</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-yellow-100 text-yellow-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['requested'] ?? 0)); ?></p>
                    <p class="admin-dashboard-stat-label">Menunggu</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-green-100 text-green-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['approved'] ?? 0)); ?></p>
                    <p class="admin-dashboard-stat-label">Disetujui</p>
                </div>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="admin-dashboard-stat-icon bg-blue-100 text-blue-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['completed'] ?? 0)); ?></p>
                    <p class="admin-dashboard-stat-label">Selesai</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" action="<?php echo e(route('admin.returns.index')); ?>" class="space-y-4 sm:space-y-0 sm:flex sm:items-end sm:space-x-4">
                <div class="flex-1">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari Retur</label>
                    <input type="text" 
                           id="search" 
                           name="search" 
                           value="<?php echo e(request('search')); ?>"
                           placeholder="Cari berdasarkan produk, toko, atau supplier..."
                           class="admin-dashboard-input">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="admin-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="requested" <?php echo e(request('status') === 'requested' ? 'selected' : ''); ?>>Menunggu</option>
                        <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Disetujui</option>
                        <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Ditolak</option>
                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Selesai</option>
                    </select>
                </div>
                <div>
                    <label for="month" class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <select id="month" name="month" class="admin-dashboard-select">
                        <?php $__currentLoopData = $availableMonths; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($month['value']); ?>" <?php echo e(($filterMonth ?? 'all') === $month['value'] ? 'selected' : ''); ?>>
                                <?php echo e($month['label']); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="flex space-x-2">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        Filter
                    </button>
                    <?php if(request()->hasAny(['search', 'status', 'month'])): ?>
                    <a href="<?php echo e(route('admin.returns.index')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        Reset
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Retur</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <!-- <th class="px-6 py-3">Toko</th> -->
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Supplier</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Tanggal Retur</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $returns ?? []; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->product->name ?? 'N/A'); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($return->product->category->name ?? 'Tanpa Kategori'); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($return->supplier->name ?? 'N/A'); ?></div>
                                <div class="text-xs text-gray-500"><?php echo e($return->supplier->contact_person ?? ''); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900 font-medium"><?php echo e(number_format($return->quantity ?? 0)); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900"><?php echo e($return->return_date ? $return->return_date->format('d/m/Y') : '-'); ?></div>
                                <?php if($return->processed_date): ?>
                                <div class="text-xs text-gray-500">Diproses: <?php echo e($return->processed_date->format('d/m/Y')); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    <?php if($return->status === 'requested'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($return->status === 'approved'): ?> bg-green-100 text-green-800
                                    <?php elseif($return->status === 'rejected'): ?> bg-red-100 text-red-800
                                    <?php elseif($return->status === 'completed'): ?> bg-blue-100 text-blue-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php if($return->status === 'requested'): ?> Menunggu
                                    <?php elseif($return->status === 'approved'): ?> Disetujui
                                    <?php elseif($return->status === 'rejected'): ?> Ditolak
                                    <?php elseif($return->status === 'completed'): ?> Selesai
                                    <?php else: ?> <?php echo e(ucfirst($return->status ?? '')); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.returns.show', $return)); ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    <?php if($return->status === 'approved'): ?>
                                    <?php if($return->isFromStore() && !$return->supplier_id): ?>
                                    <button onclick="openForwardToSupplierModal('<?php echo e($return->id); ?>', '<?php echo e($return->product->name); ?>')"
                                            class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        Teruskan ke Supplier
                                    </button>
                                    <?php endif; ?>
                                    <button onclick="openCompleteModal('<?php echo e($return->id); ?>')"
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Selesaikan
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Retur</p>
                                    <p class="mb-4">Belum ada retur yang perlu dikelola</p>
                                    <a href="<?php echo e(route('admin.returns.create')); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                                        Buat Retur Baru
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if(isset($returns) && $returns->hasPages()): ?>
            <div class="mt-6">
                <?php echo e($returns->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>





<!-- Forward to Supplier Modal -->
<div id="forwardToSupplierModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Teruskan ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeForwardToSupplierModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="forwardToSupplierForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Teruskan Retur ke Supplier</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Pilih supplier untuk meneruskan retur produk <strong id="forwardProductName"></strong>.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label for="forward_supplier_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Supplier <span class="text-red-500">*</span>
                                </label>
                                <select id="forward_supplier_id" name="supplier_id" class="admin-dashboard-select" required>
                                    <option value="">Pilih Supplier</option>
                                    <?php $__currentLoopData = \App\Models\Supplier::active()->orderBy('name')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($supplier->id); ?>"><?php echo e($supplier->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div>
                                <label for="forward_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                    Catatan (Opsional)
                                </label>
                                <textarea id="forward_notes" name="admin_notes" rows="3"
                                          class="admin-dashboard-textarea"
                                          placeholder="Tambahkan catatan untuk supplier..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeForwardToSupplierModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Teruskan ke Supplier
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>


// Forward to Supplier Modal Functions
function openForwardToSupplierModal(returnId, productName) {
    const modal = document.getElementById('forwardToSupplierModal');
    const form = document.getElementById('forwardToSupplierForm');
    const productNameElement = document.getElementById('forwardProductName');

    form.action = `/admin/returns/${returnId}/forward-to-supplier`;
    productNameElement.textContent = productName;
    modal.classList.add('active');
}

function closeForwardToSupplierModal() {
    const modal = document.getElementById('forwardToSupplierModal');
    modal.classList.remove('active');
    document.getElementById('forwardToSupplierForm').reset();
}

// Close modals when clicking outside

document.getElementById('forwardToSupplierModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeForwardToSupplierModal();
    }
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/returns/index.blade.php ENDPATH**/ ?>