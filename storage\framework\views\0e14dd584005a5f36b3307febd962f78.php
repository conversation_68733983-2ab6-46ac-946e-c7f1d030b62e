<?php $__env->startSection('title', 'Detail Pengguna - Indah Berkah Abadi'); ?>
<?php $__env->startSection('page-title', 'Detail Pengguna'); ?>

<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Detail Pengguna</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap pengguna <?php echo e($user->name); ?></p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                        </svg>
                        Edit Pengguna
                    </a>
                    <a href="<?php echo e(route('admin.users.index')); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- User Information -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Pengguna</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nama Lengkap</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-900 font-medium"><?php echo e($user->name); ?></span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-900"><?php echo e($user->email); ?></span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Peran</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full <?php echo e($user->role === 'admin' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'); ?>">
                                <?php echo e($user->role === 'admin' ? 'Administrator' : 'Pengguna Toko'); ?>

                            </span>
                        </div>
                    </div>
                </div>

                <!-- Store Information -->
                <div class="space-y-4">
                    <?php if($user->role === 'user' && $user->store): ?>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Lokasi Toko</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-900 font-medium"><?php echo e($user->store->location); ?></span>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tanggal Dibuat</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-900"><?php echo e($user->created_at->format('d M Y, H:i')); ?> WIB</span>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Terakhir Diperbarui</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span class="text-gray-900"><?php echo e($user->updated_at->format('d M Y, H:i')); ?> WIB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if($user->role === 'user'): ?>
    <!-- Store Statistics (for store users) -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Statistik Toko</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <?php
                $distributions = \App\Models\Distribution::where('store_id', $user->store_id)->get();
                $totalDistributions = $distributions->count();
                $pendingDistributions = $distributions->where('confirmed', false)->count();
                $completedDistributions = $distributions->where('confirmed', true)->count();
                $lastDistribution = $distributions->sortByDesc('created_at')->first();
            ?>
            
            <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div class="flex items-center gap-3 p-4 bg-blue-50 rounded-lg">
                    <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-900"><?php echo e($totalDistributions); ?></div>
                        <div class="text-sm text-gray-600">Total Distribusi</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-4 bg-orange-50 rounded-lg">
                    <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-900"><?php echo e($pendingDistributions); ?></div>
                        <div class="text-sm text-gray-600">Pending</div>
                    </div>
                </div>

                <div class="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-xl font-bold text-gray-900"><?php echo e($completedDistributions); ?></div>
                        <div class="text-sm text-gray-600">Selesai</div>
                    </div>
                </div>
            </div>

            <?php if($lastDistribution): ?>
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-medium text-gray-900 mb-2">Distribusi Terakhir</h4>
                <div class="text-sm text-gray-600">
                    <p><strong>Produk:</strong> <?php echo e($lastDistribution->product->name); ?></p>
                    <p><strong>Tanggal:</strong> <?php echo e($lastDistribution->date_distributed->format('d M Y')); ?></p>
                    <p><strong>Status:</strong>
                        <span class="inline-flex px-2 py-1 text-xs font-medium rounded-full <?php echo e($lastDistribution->status_badge_class); ?>">
                            <?php echo e($lastDistribution->status_label); ?>

                        </span>
                    </p>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Actions -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Aksi</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row gap-3">
                <a href="<?php echo e(route('admin.users.edit', $user)); ?>" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Edit Pengguna
                </a>
                
                <?php if($user->role === 'user'): ?>
                <a href="<?php echo e(route('admin.distributions.create', ['store_id' => $user->id])); ?>" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    Buat Distribusi
                </a>
                <?php endif; ?>

                <?php if($user->id !== auth()->id()): ?>
                <form action="<?php echo e(route('admin.users.destroy', $user)); ?>" method="POST" class="inline" onsubmit="return confirm('Apakah Anda yakin ingin menghapus pengguna ini?')">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                        Hapus Pengguna
                    </button>
                </form>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/users/show.blade.php ENDPATH**/ ?>